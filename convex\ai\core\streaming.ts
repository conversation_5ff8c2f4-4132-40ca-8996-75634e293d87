"use node";

import {
  step<PERSON>ount<PERSON><PERSON>,
  has<PERSON><PERSON><PERSON><PERSON>,
  Experimental_Agent as Agent,
  streamText,
} from "ai";
import { api } from "../../_generated/api";
import { createAvailableTools } from "../tools";
import {
  createAIModel,
  getProvider<PERSON><PERSON><PERSON>ey,
  getDefaultModel,
  getModelInfo,
  SupportedProvider,
} from "../providers";
import { getPrioritizedProviders } from "../../../src/lib/models";
import { PROVIDER_BASE_URLS } from "../providers/constants";
import { getUserFriendlyErrorMessage } from "../utils/errors";
import { classifyTask } from "./utils";

// Allow streaming responses up to 1 hour (3600 seconds) for deep research models
export const maxDuration = 3600;

export async function generateStreamingResponse(
  ctx: any,
  args: {
    conversationId: string;
    branchId?: string;
    messages: Array<{
      role: string;
      content:
        | string
        | Array<{
            type: "text" | "image" | "file";
            text?: string;
            image?: string;
            file?: string;
            data?: string;
          }>;
    }>;
    provider?: string;
    model?: string;
    temperature?: number;
    enabledTools?: string[];
    thinkingBudget?: string | number;
    persona?: string;
    recipe?: string;
  }
) {
  let originalProvider = (args.provider as SupportedProvider) || "openai";
  let model = args.model ?? getDefaultModel(originalProvider);
  const temperature = args.temperature ?? 1;
  const enabledTools = args.enabledTools ?? [];
  const branchId = args.branchId;
  const personaId = args.persona || "none";
  const recipeId = args.recipe || "none";

  // After model assignment
  if (model === "auto") {
    const classified = await classifyTask(
      ctx,
      args.messages[args.messages.length - 1].content as string
    );
    model = classified.model;
    originalProvider = classified.provider as SupportedProvider;
  }

  // Get prioritized providers for this model, with user's preferred provider first if specified
  const providersToTry = getPrioritizedProviders(model, args.provider);

  // Filter out any "unknown" providers and validate we have valid providers
  const validProvidersToTry = providersToTry.filter(
    (p) => p.provider !== "unknown"
  );

  if (validProvidersToTry.length === 0) {
    // No valid providers found, create error message
    const errorMessage = `Model "${model}" is not supported by any available providers. Please check the model name or try a different model.`;

    await ctx.runMutation(api.messages.add, {
      conversationId: args.conversationId,
      branchId: args.branchId,
      role: "assistant",
      content: errorMessage,
      isError: true,
    });

    return { error: errorMessage };
  }

  let lastError: Error | null = null;
  const attemptedProviders: string[] = [];
  let actualProvider: string = validProvidersToTry[0].provider;
  let actualModelId: string = validProvidersToTry[0].modelId;

  // Check if the model supports tools early to determine prompt structure
  const modelInfo = getModelInfo(model);
  const modelSupportsTools = modelInfo.supportsTools;

  let messageId: any = null;
  let usingUserKey = false;
  let abortController: AbortController | null = null;

  // Provider fallback loop
  for (
    let providerIndex = 0;
    providerIndex < validProvidersToTry.length;
    providerIndex++
  ) {
    const currentProviderInfo = validProvidersToTry[providerIndex];
    actualProvider = currentProviderInfo.provider;
    actualModelId = currentProviderInfo.modelId;

    console.log(
      `[Provider Fallback] Attempting provider ${actualProvider} (${providerIndex + 1}/${validProvidersToTry.length}) for model ${model}`
    );

    try {
      // Check if this is a custom provider
      let customProvider = null;
      if (
        typeof actualProvider === "string" &&
        ![
          "openai",
          "anthropic",
          "google",
          "openrouter",
          "groq",
          "deepseek",
          "grok",
          "cohere",
          "mistral",
          "cerebras",
          "github",
        ].includes(actualProvider)
      ) {
        // This is a custom provider, fetch its configuration
        customProvider = await ctx.runQuery(api.customProviders.getByName, {
          name: actualProvider,
        });
        if (!customProvider) {
          const error = new Error(
            `Custom provider '${actualProvider}' not found`
          );
          lastError = error;
          attemptedProviders.push(actualProvider);
          console.log(
            `[Provider Fallback] Custom provider ${actualProvider} not found, trying next provider`
          );
          continue;
        }
      }
      // Create an AbortController for timeout handling
      abortController = new AbortController();

      // Set timeout based on provider and model - Deep research models need much longer timeouts
      let timeoutMs = 300000; // Default 5 minutes
      if (actualProvider === "google") {
        timeoutMs = 600000; // 10 minutes for Google
      }
      if (actualModelId.includes("deep-research")) {
        timeoutMs = 3600000; // 1 hour for deep research models
      }
      const _timeoutId = setTimeout(() => {
        if (abortController && !abortController.signal.aborted) {
          abortController.abort(
            new Error(`Request timeout after ${timeoutMs / 1000} seconds`)
          );
        }
      }, timeoutMs);

      // Clear any existing cancellation flag to start fresh
      await ctx.runMutation(api.conversations.clearCancellation, {
        conversationId: args.conversationId,
      });
      await ctx.runMutation(api.conversations.setGenerationState, {
        conversationId: args.conversationId,
        isGenerating: true,
      });

      // Process multimodal content if supported

      // Convert storage URLs to public URLs for multimodal models
      const processedMessages = await Promise.all(
        args.messages.map(async (message) => {
          if (Array.isArray(message.content)) {
            const processedContent = await Promise.all(
              message.content.map(async (part) => {
                if (part.type === "image") {
                  const imageValue = part.image;

                  // Detect Convex storage IDs if the value is not already an http(s) or data URI
                  const isStorageId =
                    typeof imageValue === "string" &&
                    !imageValue.startsWith("http") &&
                    !imageValue.startsWith("data:");

                  if (isStorageId) {
                    try {
                      const publicUrl = await ctx.storage.getUrl(
                        imageValue as any
                      );
                      if (publicUrl) {
                        return {
                          type: "image",
                          image: publicUrl,
                        };
                      } else {
                        console.warn(
                          `Failed to get public URL for storage ID: ${imageValue}`
                        );
                        return part;
                      }
                    } catch (error) {
                      console.error(
                        `Error getting public URL for storage ID ${imageValue}:`,
                        error
                      );
                      return part;
                    }
                  }

                  return part;
                } else if (part.type === "file") {
                  const fileValue =
                    (part as any).data ??
                    (part as any).file?.data ??
                    (part as any).file;

                  // Treat as storage ID if not a URL/data URI
                  const isStorageId =
                    typeof fileValue === "string" &&
                    !fileValue.startsWith("http") &&
                    !fileValue.startsWith("data:");

                  if (isStorageId) {
                    try {
                      // Get file metadata using the system table (recommended approach)
                      const fileMetadata = await ctx.db.system.get(fileValue);
                      const publicUrl = await ctx.storage.getUrl(fileValue);

                      if (publicUrl && fileMetadata) {
                        return {
                          type: "file",
                          data: publicUrl,
                          mediaType:
                            fileMetadata.contentType ??
                            "application/octet-stream",
                        };
                      } else {
                        console.warn(
                          `Failed to get public URL or metadata for file storage ID: ${fileValue}`
                        );
                        return part;
                      }
                    } catch (error) {
                      console.error(
                        `Error getting public URL for file storage ID ${fileValue}:`,
                        error
                      );
                      return part;
                    }
                  }

                  return part;
                }
                return part;
              })
            );
            return { ...message, content: processedContent };
          }
          return message;
        })
      );

      // After processing messages, before credit check

      // Parallelize independent queries
      const [userPreferences, userInstructions, apiKeyRecord] =
        await Promise.all([
          ctx.runQuery(api.preferences.get),
          ctx.runQuery(api.userInstructions.get),
          customProvider
            ? null
            : ctx.runQuery(api.apiKeys.getByProvider, {
                provider: actualProvider,
              }),
        ]);

      // Then, compute apiKey
      let apiKey: string;
      if (customProvider) {
        apiKey = customProvider.apiKey;
        usingUserKey = true;
      } else {
        const { apiKey: retrievedApiKey, usingUserKey: userKeyFlag } =
          getProviderApiKey(actualProvider as SupportedProvider, apiKeyRecord);
        apiKey = retrievedApiKey;
        usingUserKey = userKeyFlag;
      }

      if (!apiKey) {
        const error = new Error(
          `No API key available for ${actualProvider}. Please configure your API key in settings or use a provider with built-in support.`
        );
        lastError = error;
        attemptedProviders.push(actualProvider);
        console.log(
          `[Provider Fallback] No API key for ${actualProvider}, trying next provider`
        );
        continue;
      }

      // Only check usage limits if using built-in keys
      if (!usingUserKey) {
        // Estimate token usage for credit check (rough estimate)
        const estimatedInputTokens = args.messages.reduce((total, msg) => {
          const content =
            typeof msg.content === "string"
              ? msg.content
              : msg.content
                  .map((c) => (c.type === "text" ? (c.text ?? "") : ""))
                  .join(" ");
          return total + Math.ceil(content.length / 4); // Rough estimate: 4 chars per token
        }, 0);
        const estimatedOutputTokens = 2000; // Conservative estimate for credit check

        const creditCheck = await ctx.runQuery(
          api.usage.checkCreditsAvailable,
          {
            model: actualModelId,
            estimatedInputTokens,
            estimatedOutputTokens,
            provider: actualProvider,
          }
        );

        if (!creditCheck.hasCredits) {
          const error = new Error(
            `Insufficient credits. Required: ${creditCheck.requiredCredits}, Available: ${creditCheck.availableCredits}. Add your own API keys in settings for unlimited usage.`
          );
          lastError = error;
          attemptedProviders.push(actualProvider);
          console.log(
            `[Provider Fallback] Insufficient credits for ${actualProvider}, trying next provider`
          );
          continue;
        }

        if (creditCheck.wouldExceedSpending) {
          const error = new Error(
            `This request would exceed your monthly spending limit. Add your own API keys in settings for unlimited usage.`
          );
          lastError = error;
          attemptedProviders.push(actualProvider);
          console.log(
            `[Provider Fallback] Would exceed spending limit for ${actualProvider}, trying next provider`
          );
          continue;
        }
      }

      // -------------------------------------------------------------------
      // User identity is needed later for Vectorize indexing regardless of
      // whether any tools are enabled, but expensive server queries (MCP/n8n)
      // should only run when required. We therefore retrieve userId once here
      // and reuse it later, while wrapping the heavier queries in a guard.
      // -------------------------------------------------------------------
      const { getAuthUserId } = await import("@convex-dev/auth/server");
      const userId = await getAuthUserId(ctx);

      let availableTools: Record<string, any> = {};
      let _hasTools = false;

      // Skip expensive server look-ups entirely when the user hasn’t enabled
      // any tools – this avoids multiple redundant DB round-trips on first use.
      if (enabledTools.length > 0) {
        let mcpServers: any[] = [];
        let n8nServers: any[] = [];
        let n8nWorkflows: any[] = [];

        if (userId) {
          // These queries are only needed if we might actually construct tools.
          mcpServers = await ctx.runQuery(api.mcpServers.listEnabled, {});
          n8nServers = await ctx.runQuery(api.n8nServers.listEnabled, {});
          n8nWorkflows = await ctx
            .runQuery(api.n8nWorkflows.listEnabled, {})
            .catch(() => []);
        }

        // Build the tool map (may still return empty if model doesn’t support tools)
        availableTools = await createAvailableTools(
          ctx,
          enabledTools,
          model,
          usingUserKey,
          mcpServers,
          n8nServers,
          n8nWorkflows
        );

        _hasTools = Object.keys(availableTools).length > 0;
      }

      // Track generation start time and detailed timing
      const generationStartTime = Date.now();
      let _timeToFirstToken: number | undefined;
      let _timeToFirstContent: number | undefined;
      let _timeToFirstTool: number | undefined;
      let _reasoningStartTime: number | undefined;
      let _reasoningEndTime: number | undefined;
      let _toolExecutionStartTime: number | undefined;
      let _toolExecutionEndTime: number | undefined;
      // Track finish reason for each agent step
      let stepFinishReason: string | undefined;

      // Create AI model instance with native thinking support
      const {
        model: streamingAiModel,
        providerOptions,
        hasNativeThinking,
      } = createAIModel({
        provider: actualProvider,
        model: actualModelId,
        apiKey,
        baseURL: customProvider
          ? customProvider.baseURL
          : PROVIDER_BASE_URLS[
              actualProvider as keyof typeof PROVIDER_BASE_URLS
            ],
        temperature,
        thinkingBudget: args.thinkingBudget,
        enabledTools,
        customProvider: customProvider
          ? {
              name: customProvider.name,
              baseURL: customProvider.baseURL,
              models: customProvider.models,
            }
          : undefined,
      });

      // Configure model with provider options for thinking support
      let enhancedModel = streamingAiModel;
      if (providerOptions && Object.keys(providerOptions).length > 0) {
        // For models that support thinking, we need to wrap the model with provider options
        const modelInfo = getModelInfo(model);
        if (modelInfo.supportsThinking && args.thinkingBudget) {
          console.log(
            `[Thinking Debug] Configuring enhanced model with provider options:`,
            providerOptions
          );

          // For AI SDK v5 Agent pattern, we need to apply provider options differently
          // Instead of wrapping the model, we'll ensure the options are passed to stream()
          enhancedModel = streamingAiModel;
        }
      }

      // Get user preferences and instructions to build system prompt
      // const userPreferences = await ctx.runQuery(api.preferences.get);
      // const userInstructions = await ctx.runQuery(api.userInstructions.get);

      // Prepare messages with system prompt if enabled
      let messagesWithSystemPrompt = [...processedMessages];

      // OpenRouter provider requires content parts arrays instead of plain strings.
      if (actualProvider === "openrouter") {
        messagesWithSystemPrompt = messagesWithSystemPrompt.map((msg: any) => {
          if (typeof msg.content === "string") {
            return {
              ...msg,
              content: [
                {
                  type: "text",
                  text: msg.content,
                },
              ],
            };
          }
          // Leave array content unchanged
          return msg;
        });
      }

      // Check if there's already a system message
      const hasSystemMessage = processedMessages.some(
        (msg) => msg.role === "system"
      );

      if (!hasSystemMessage) {
        let systemContent = "";

        // Include today's date in the system prompt (human-readable format)
        const today = new Date();
        const todayDate = today.toLocaleDateString("en-US", {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
        });

        if (
          userPreferences?.useCustomSystemPrompt &&
          userPreferences?.systemPrompt
        ) {
          // Use custom system prompt
          systemContent =
            `Current date: ${todayDate}\n\n` + userPreferences.systemPrompt;
        } else {
          // Use default system prompt when custom is disabled
          if (modelSupportsTools) {
            systemContent =
              `Current date: ${todayDate}\n\n` +
              "You are ErzenAI — a world-class autonomous AI agent. You think step-by-step, plan before acting, and judiciously call external tools to accomplish the user's goals.\n\n" +
              "### Core Principles\n" +
              "- **Outcome Focused**: Always deliver useful, actionable results.\n" +
              "- **Ethical & Safe**: Respect privacy, honesty, and safety.\n" +
              "- **Continuous Learning**: Reflect on each answer and improve.\n\n" +
              "### Working Method\n" +
              "1. **Plan first**: Draft a concise plan in your native thinking channel before responding.\n" +
              "2. **Tool usage**: Briefly tell the user in natural language what you are doing (e.g., 'Checking the weather in Berlin...', 'Searching for the latest AI news...'). Do NOT show any raw JSON or tool arguments. Invoke the tool silently.\n" +
              "3. **Reflect**: Inspect tool output, decide if more actions are required, iterate.\n" +
              "4. **Deliver**: Craft a clear, helpful answer.\n\n" +
              "### Markdown Style Guide\n" +
              "- Put **one blank line** after every heading, paragraph, list or code block.\n" +
              "- Use ### (h3) or smaller headers; never # or ##.\n" +
              "- Use **bold** and *italics* sparingly for emphasis.\n" +
              "- Wrap code in fenced blocks with language tags and a blank line before and after.\n" +
              "- Use - bullets or 1. numbered lists with spacing.\n" +
              "- Insert horizontal rules (---) to separate major sections.\n" +
              "- Keep lines short (≤100 characters) and avoid trailing spaces.\n\n" +
              "Respond in the same language as the user and favour clarity over verbosity.";
          } else {
            systemContent =
              `Current date: ${todayDate}\n\n` +
              "You are ErzenAI, an intelligent AI assistant that reasons step-by-step and delivers clear, well-structured answers.\n\n" +
              "### Core Principles\n" +
              "- **Outcome Focused**: Provide practical, actionable information.\n" +
              "- **Ethical & Safe**: Be truthful, respectful and protect user privacy.\n" +
              "- **Continuous Learning**: Reflect on feedback and improve.\n\n" +
              "### Working Method\n" +
              "1. **Plan first**: Briefly outline your answer in the native thinking channel.\n" +
              "2. **Research**: Rely on internal knowledge and logical reasoning.\n" +
              "3. **Deliver**: Craft a concise, helpful response.\n\n" +
              "### Markdown Style Guide\n" +
              "- Put **one blank line** after every heading, paragraph, list or code block.\n" +
              "- Use ### (h3) or smaller headers; never # or ##.\n" +
              "- Use **bold** and *italics* sparingly.\n" +
              "- Wrap code in fenced blocks with language tags.\n" +
              "- Use - bullets or 1. numbered lists with spacing.\n" +
              "- Insert horizontal rules (---) to separate sections.\n" +
              "- Keep lines short (≤100 characters) and avoid trailing spaces.\n\n" +
              "Respond in the same language as the user and favour clarity over verbosity.";
          }
        }

        // Append guidelines for Mermaid diagrams and language adaptation
        systemContent +=
          "\n\n## Diagrams and Language\n- When presenting charts or diagrams, embed them using Mermaid syntax inside ```mermaid``` code blocks.\n- Always respond in the same language that the user used in their last message.";

        // Append persona prompt if provided
        const PERSONA_PROMPTS: Record<string, string> = {
          companion:
            "You are the user's compassionate companion. Respond with warmth, empathy and encouragement. Prioritise emotional support over facts.",
          friend:
            "You are the user's close friend. Keep the tone casual, supportive and lightly humorous. Use informal language, contractions and emojis when appropriate.",
          comedian:
            "You are a stand-up comedian. Deliver responses with wit and humour while still addressing the user's topic. Make sure jokes are light-hearted and never offensive.",
          not_a_doctor:
            "You are NOT a medical professional. If the user requests medical advice you must disclaim you are not a doctor and encourage consulting a qualified physician. Provide general information only.",
          not_a_therapist:
            "You are NOT a mental-health professional. Provide supportive, non-clinical responses and encourage the user to seek professional help for serious issues.",
        };

        if (personaId && personaId !== "none" && PERSONA_PROMPTS[personaId]) {
          systemContent = PERSONA_PROMPTS[personaId] + "\n\n" + systemContent;
        }

        // Append recipe prompt if provided
        const RECIPE_PROMPTS: Record<string, string> = {
          summarise:
            "Whenever the user provides text, summarise it concisely using bullet-points. If the text is short, provide a one-sentence summary.",
          translate_es:
            "Translate all user input into Spanish. Respond ONLY with the translation, no explanations.",
          brainstorm:
            "Generate a creative list of at least 10 varied ideas that satisfy the user's request. Encourage originality and diversity.",
          email_draft:
            "Craft a professional, well-structured email based on the user's instructions. Use a polite tone and clear formatting.",
          agent:
            "You are an autonomous multi-tool AI agent, equipped with specialised tools such as **thinking** (deep internal reasoning) and **plan** (multi-step orchestration). When `agent` mode is active:\n\n1. **Analyse** the user request and break it into concise, actionable TODO items.\n2. **Draft an execution strategy** by *calling the `plan` tool first* for any task that needs three-plus steps or several tools.  Show the plan under `### Plan` using markdown check-boxes (`- [ ]`).\n3. **Execute** each TODO sequentially.  Call external tools whenever beneficial.  For complex reasoning, invoke `thinking` to reflect step-by-step before acting.\n4. **Reflect & update**: After each tool call, summarise results, tick off completed items (`- [x]`), and adjust remaining steps if needed.\n5. **Deliver** a polished, comprehensive answer when finished and mark the plan as ✓ **Done**.\n\nGuidelines:\n• Use `thinking` freely for ambiguity or deep analysis.\n• Use `plan` whenever coordinating multiple tools or unclear paths.\n• Supply only the JSON-schema parameters required by each tool.\n• Keep users informed of progress; internal reasoning stays within `thinking`.\n• Be efficient – avoid redundant calls and unnecessary verbosity.\n\nIterate, reflect, adapt – solve the task completely.",
        };

        if (recipeId && recipeId !== "none" && RECIPE_PROMPTS[recipeId]) {
          systemContent += "\n\n" + RECIPE_PROMPTS[recipeId];
        }

        // Add user instructions if available
        if (userInstructions && userInstructions.trim()) {
          systemContent +=
            "\n\nAdditional user instructions:\n" + userInstructions;
        }

        // Add system prompt at the beginning
        messagesWithSystemPrompt = [
          {
            role: "system" as const,
            content: systemContent,
          },
          ...processedMessages,
        ];
      }

      // Filter out OpenAI built-in tools from availableTools since they're handled in providerOptions
      const builtInOpenAITools = ["web_search_preview", "code_interpreter"];
      const filteredTools = Object.fromEntries(
        Object.entries(availableTools).filter(
          ([key]) => !builtInOpenAITools.includes(key)
        )
      );
      const hasFilteredTools = Object.keys(filteredTools).length > 0;

      // Decide whether we need multi-modal support (any message content is an array of parts)
      const _needsMultiModal = messagesWithSystemPrompt.some((m: any) =>
        Array.isArray(m.content)
      );

      // Generate the response using streamText (for multimodal) or agent (for tool-heavy text)
      const streamTextConfig: any = {
        model: streamingAiModel,
        messages: messagesWithSystemPrompt as any,
        temperature,
        // V5 Agentic Loop Control: Replace maxSteps with stopWhen
        stopWhen: [
          stepCountIs(35), // Maximum 35 steps (equivalent to old maxSteps)
          hasToolCall("finalAnswer"), // Stop if finalAnswer tool is called
        ],
        // V5 prepareStep: Dynamic step control
        prepareStep: ({
          stepNumber,
          messages,
        }: {
          stepNumber: number;
          messages: any[];
        }) => {
          // For long conversations, compress context
          if (messages.length > 20) {
            return {
              messages: messages.slice(-15), // Keep last 15 messages
            };
          }

          // Return undefined to use default settings
          return undefined;
        },
        abortSignal: abortController.signal,
        // Include native thinking support via provider options
        providerOptions,
      };

      // Remove previously added minimal objects and instead build using OpenAI SDK
      /* OPENAI BUILT-IN TOOLS FOR DEEP RESEARCH MODELS */
      if (
        actualProvider === "openai" &&
        actualModelId.includes("deep-research")
      ) {
        // Ensure openai provider tools are available
        try {
          const builtInTools: any = {};
          const openaiProvider: any = await import("@ai-sdk/openai");
          const openaiInstance = openaiProvider.openai;

          // Add web_search_preview tool definition
          if (typeof openaiInstance.tools?.webSearchPreview === "function") {
            builtInTools.web_search_preview =
              openaiInstance.tools.webSearchPreview();
          }

          // Add code_interpreter tool definition
          if (typeof openaiInstance.tools?.codeInterpreter === "function") {
            builtInTools.code_interpreter =
              openaiInstance.tools.codeInterpreter({});
          }

          // Merge with any other filtered tools
          streamTextConfig.tools = {
            ...(hasFilteredTools ? filteredTools : {}),
            ...builtInTools,
          };
        } catch (err) {
          console.warn(
            "Failed to load OpenAI built-in tools, falling back to minimal stubs:",
            err
          );
          streamTextConfig.tools = {
            ...(hasFilteredTools ? filteredTools : {}),
            web_search_preview: { type: "web_search_preview" },
            code_interpreter: {
              type: "code_interpreter",
              container: { type: "auto" },
            },
          };
        }
      } else if (modelSupportsTools && hasFilteredTools) {
        streamTextConfig.tools = filteredTools;
        console.log(
          `[Tools Enabled] ${Object.keys(filteredTools).length} tools available:`,
          Object.keys(filteredTools)
        );
      } else {
        console.log(
          `[Tools Disabled] Model supports tools: ${modelSupportsTools}, Has tools: ${hasFilteredTools}`
        );
      }

      // Create an AI Agent using v5 Agent abstraction for better agentic control
      console.log(
        `[Agent Setup] Model: ${actualModelId}, Tools available: ${hasFilteredTools ? Object.keys(filteredTools).length : 0}`
      );
      if (hasFilteredTools) {
        console.log(
          `[Agent Tools] Available tools:`,
          Object.keys(filteredTools)
        );
      }

      // Extract system prompt properly
      const systemMessage = messagesWithSystemPrompt.find(
        (m: any) => m.role === "system"
      );
      const systemPrompt =
        typeof systemMessage?.content === "string"
          ? systemMessage.content
          : `You are ErzenAI, an advanced autonomous AI assistant powered by state-of-the-art language models. You are designed to be helpful, capable, and proactive in solving any task or answering any question.

**Core Capabilities:**
• **Autonomous Task Execution**: You can break down complex tasks into steps and execute them systematically using your available tools
• **Real-time Information**: Access current information through web search, weather data, and other live sources
• **Mathematical Computing**: Perform complex calculations and mathematical operations
• **Research & Analysis**: Conduct comprehensive research on any topic with multiple perspectives
• **Creative Problem Solving**: Think creatively and find innovative solutions to challenges
• **Tool Integration**: Seamlessly use multiple tools together to accomplish complex workflows

**Your Approach:**
1. **Listen Carefully**: Understand exactly what the user wants to accomplish
2. **Plan Strategically**: Break complex requests into manageable steps
3. **Execute Autonomously**: Use your tools proactively without asking for permission unless absolutely necessary
4. **Be Thorough**: Don't just give partial answers - complete the full task
5. **Stay Helpful**: Always aim to exceed user expectations

**Available Tools:**
You have access to powerful tools including web search, weather data, mathematical calculations, research capabilities, and more. Use these tools confidently and strategically to provide comprehensive, accurate, and helpful responses.

**Personality:**
• Be conversational and engaging
• Show enthusiasm for helping solve problems
• Explain your reasoning when helpful
• Admit limitations honestly when they exist
• Take initiative to provide more value than requested

Remember: You are not just answering questions - you are an autonomous agent capable of completing complex tasks, conducting research, solving problems, and providing comprehensive assistance. Always strive to be as helpful and capable as possible!`;

      const _agent = new Agent({
        model: enhancedModel,
        system: systemPrompt,
        tools: hasFilteredTools ? filteredTools : {},
        stopWhen: [
          stepCountIs(35), // Maximum 35 steps
          hasToolCall("finalAnswer"), // Stop if finalAnswer tool is called'
        ],
        prepareStep: ({
          stepNumber,
          messages,
        }: {
          stepNumber: number;
          messages: any[];
        }) => {
          // For long conversations, compress context
          if (messages.length > 20) {
            return {
              messages: messages.slice(-15), // Keep last 15 messages
            };
          }

          return undefined;
        },
      });

      // Use agent.stream() with the current message prompt
      // For Agent, we'll include conversation context in the system prompt instead of messages
      const userMessages = messagesWithSystemPrompt.filter(
        (m: any) => m.role !== "system"
      );
      const lastMessage = userMessages[userMessages.length - 1];
      const prompt =
        typeof lastMessage?.content === "string"
          ? lastMessage.content
          : JSON.stringify(lastMessage?.content || "");

      // Process the stream manually (Convex-compatible approach with v5 stream parts)
      let accumulatedContent = "";
      let accumulatedReasoning: string | undefined = undefined;
      const toolCalls: any[] = [];
      const contentSequence: any[] = [];

      let hasCreatedMessage = false;
      let result: any; // Declare result in scope for later access

      // Create message immediately to ensure we capture all content
      messageId = await ctx.runMutation(api.messages.prepareAssistantMessage, {
        conversationId: args.conversationId,
        branchId: branchId,
      });
      hasCreatedMessage = true;

      // Streaming optimization: Batch text updates for performance
      let lastUpdateTime = 0;
      const UPDATE_THROTTLE_MS = 100; // Update every 100ms for smooth streaming

      try {
        // In AI SDK v5, Agent.stream() can fail synchronously with auth errors
        const streamConfig: any = {
          prompt,
          onStepFinish: ({ finishReason }: { finishReason?: string }) => {
            if (finishReason) {
              stepFinishReason = finishReason;
            }
          },
        };

        // Pass provider-specific options for thinking/reasoning support
        if (providerOptions && Object.keys(providerOptions).length > 0) {
          console.log(
            `[Thinking Debug] Applying provider options to stream config:`,
            providerOptions
          );
          // In AI SDK v5 Agent pattern, provider options should be passed as part of the config
          streamConfig.providerOptions = providerOptions;
        }

        // Temporarily disable Experimental_Agent path and always use native streamText
        result = streamText(streamTextConfig);

        for await (const part of result.fullStream) {
          // Check for abort signal
          if (abortController?.signal.aborted) {
            const abortReason =
              abortController.signal.reason?.message || "Request was aborted";
            throw new Error(`Stream aborted: ${abortReason}`);
          }

          // Message already created early, no need to check for creation anymore

          // Handle different part types in v5 with optimized incremental updates for real-time streaming
          if (part.type === "text" || part.type === "text-delta") {
            const textContent = part.text || part.textDelta || "";

            // 🔥 FILTER OUT TOOL CALL JSON: Don't add tool call JSON to content
            let shouldAddTextContent = true;
            try {
              const trimmed = textContent.trim();

              // Check if this text content looks like tool call JSON
              if (
                (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
                (trimmed.startsWith("[") && trimmed.endsWith("]"))
              ) {
                const parsed = JSON.parse(trimmed);

                // Check for various tool call patterns
                const looksLikeTool =
                  // Standard tool call format
                  (parsed &&
                    typeof parsed === "object" &&
                    "name" in parsed &&
                    "arguments" in parsed) ||
                  // Function call format
                  (parsed &&
                    typeof parsed === "object" &&
                    "function" in parsed) ||
                  // Tool invocation format
                  (parsed && typeof parsed === "object" && "tool" in parsed) ||
                  // Weather tool specific format
                  (parsed &&
                    typeof parsed === "object" &&
                    "date" in parsed &&
                    "forecast_type" in parsed &&
                    "location" in parsed) ||
                  // Search tool format
                  (parsed &&
                    typeof parsed === "object" &&
                    "query" in parsed &&
                    typeof parsed.query === "string") ||
                  // Calculator tool format
                  (parsed &&
                    typeof parsed === "object" &&
                    "expression" in parsed) ||
                  // Generic tool parameter format
                  (parsed &&
                    typeof parsed === "object" &&
                    ("input" in parsed ||
                      "prompt" in parsed ||
                      "text" in parsed ||
                      "url" in parsed ||
                      "code" in parsed)) ||
                  // Array of tool calls
                  (Array.isArray(parsed) &&
                    parsed.length > 0 &&
                    parsed.every(
                      (item) =>
                        item &&
                        typeof item === "object" &&
                        ("name" in item || "function" in item || "tool" in item)
                    ));

                if (looksLikeTool) {
                  console.log(
                    `[Text Content Filter] Filtered out tool call JSON from text content:`,
                    trimmed.substring(0, 100)
                  );
                  shouldAddTextContent = false;
                }
              }
            } catch {
              // If parsing fails, treat as regular content
            }

            if (shouldAddTextContent) {
              accumulatedContent += textContent;

              // Update contentSequence (merge consecutive content chunks)
              if (
                contentSequence.length > 0 &&
                contentSequence[contentSequence.length - 1].type === "content"
              ) {
                contentSequence[contentSequence.length - 1].text += textContent;
              } else {
                contentSequence.push({
                  type: "content",
                  text: textContent,
                  timestamp: Date.now(),
                });
              }
            }

            // 🔥 THROTTLED STREAMING: Update text every 100ms for smooth performance
            const now = Date.now();
            if (
              hasCreatedMessage &&
              messageId &&
              now - lastUpdateTime >= UPDATE_THROTTLE_MS
            ) {
              const updateObj: any = {
                messageId,
                content: accumulatedContent,
                toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
                contentSequence: contentSequence,
              };

              if (accumulatedReasoning) {
                updateObj.thinking = accumulatedReasoning;
              }

              await ctx.runMutation(api.messages.update, updateObj);
              lastUpdateTime = now;
              console.log(
                `[Streaming Update] Text: ${accumulatedContent.length} chars`
              );
            }
          } else if (
            part.type === "reasoning" ||
            part.type === "reasoning-delta"
          ) {
            const reasoningContent =
              part.text ||
              part.textDelta ||
              part.reasoningDelta ||
              part.reasoningText ||
              "";

            console.log(
              `[Reasoning Debug] Captured reasoning content:`,
              reasoningContent
            );

            accumulatedReasoning =
              (accumulatedReasoning || "") + reasoningContent;

            console.log(
              `[Reasoning Debug] Total accumulated reasoning: ${(accumulatedReasoning || "").length} chars`
            );

            // 🔥 THROTTLED STREAMING: Update reasoning every 100ms for smooth performance
            const now = Date.now();
            if (
              hasCreatedMessage &&
              messageId &&
              now - lastUpdateTime >= UPDATE_THROTTLE_MS
            ) {
              const updateObj: any = {
                messageId,
                content: accumulatedContent,
                toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
                contentSequence: contentSequence,
              };

              if (accumulatedReasoning) {
                updateObj.thinking = accumulatedReasoning;
                console.log(
                  `[Reasoning Debug] Storing thinking in DB: ${accumulatedReasoning.substring(0, 100)}...`
                );
              }

              await ctx.runMutation(api.messages.update, updateObj);
              lastUpdateTime = now;
              console.log(
                `[Streaming Update] Reasoning: ${(accumulatedReasoning || "").length} chars`
              );
            }
          } else if (part.type === "reasoning-start") {
            // Mark start of reasoning - we'll collect content between start and end
          } else if (part.type === "reasoning-end") {
            // Mark end of reasoning
          } else if (part.type === "tool-call") {
            const toolCall = {
              id: part.toolCallId,
              name: part.toolName,
              arguments: JSON.stringify(part.input),
              startTime: Date.now(),
            };
            toolCalls.push(toolCall);

            // Add tool placeholder to contentSequence
            contentSequence.push({
              type: "tool",
              toolCallId: part.toolCallId,
              timestamp: Date.now(),
            });

            // 🔥 INCREMENTAL UPDATE: Show tool call immediately when it starts
            if (hasCreatedMessage && messageId) {
              const updateObj: any = {
                messageId,
                content: accumulatedContent,
                toolCalls: toolCalls,
                contentSequence: contentSequence,
              };

              if (accumulatedReasoning) {
                updateObj.thinking = accumulatedReasoning;
              }

              await ctx.runMutation(api.messages.update, updateObj);
              console.log(
                `[Streaming Update] Tool Call Started: ${part.toolName}`
              );
            }
          } else if (part.type === "tool-result") {
            // Update the corresponding tool call with result (v5 uses 'output' instead of 'result')
            const toolCall = toolCalls.find((tc) => tc.id === part.toolCallId);
            if (toolCall) {
              const output = part.output || part.result; // Handle both v4/v5 formats
              toolCall.result =
                typeof output === "string" ? output : JSON.stringify(output);
              toolCall.endTime = Date.now();

              // If this is the thinking tool, append to reasoning stream
              if (part.toolName === "thinking") {
                const thinkingText =
                  typeof output === "string"
                    ? output
                    : JSON.stringify(output, null, 2);
                accumulatedReasoning =
                  (accumulatedReasoning || "") + `\n${thinkingText}`;
              }

              // 🔥 INCREMENTAL UPDATE: Show tool result immediately when it completes
              if (hasCreatedMessage && messageId) {
                const updateObj: any = {
                  messageId,
                  content: accumulatedContent,
                  toolCalls: toolCalls,
                  contentSequence: contentSequence,
                };

                // Only include thinking/reasoning if user has enabled it
                if (accumulatedReasoning) {
                  updateObj.thinking = accumulatedReasoning;
                }

                await ctx.runMutation(api.messages.update, updateObj);
                console.log(
                  `[Streaming Update] Tool Result Completed: ${toolCall.name}`
                );
              }
            }
          } else if (part.type === "error") {
            // v5 AI SDK: Handle error parts in the stream
            console.error(
              `[Stream Error Part] Error in stream for ${actualProvider}/${actualModelId}:`,
              part.error
            );

            // Extract error message from the error part
            const errorMessage =
              part.error?.message ||
              part.error ||
              "Unknown streaming error occurred";

            // Create error message if we haven't created a message yet
            if (!hasCreatedMessage) {
              messageId = await ctx.runMutation(
                api.messages.prepareAssistantMessage,
                {
                  conversationId: args.conversationId,
                  branchId: branchId,
                }
              );
              hasCreatedMessage = true;
            }

            // Update message with error content
            if (messageId) {
              const updateObj: any = {
                messageId,
                content: accumulatedContent || `Error: ${errorMessage}`,
                toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
                contentSequence: contentSequence,
                isError: true,
                generationMetrics: {
                  provider: actualProvider,
                  model: actualModelId,
                  generationTimeMs: Date.now() - generationStartTime,
                  temperature,
                  attemptedProviders: attemptedProviders.concat(actualProvider),
                  fallbackUsed: attemptedProviders.length > 0,
                },
              };

              if (accumulatedReasoning) {
                updateObj.thinking = accumulatedReasoning;
              }

              await ctx.runMutation(api.messages.update, updateObj);
            }

            // Clear generation state
            await ctx.runMutation(api.conversations.setGenerationState, {
              conversationId: args.conversationId,
              isGenerating: false,
            });

            // Throw the error from the stream part to trigger provider fallback
            throw new Error(errorMessage);
          } else {
            // 🔥 FALLBACK: Try to extract text content from any unhandled stream parts
            const fallbackContent =
              part.text ||
              part.textDelta ||
              part.content ||
              part.delta ||
              (typeof part.data === "string" ? part.data : "") ||
              part.textContent ||
              "" ||
              "";

            if (fallbackContent) {
              console.log(
                `[Fallback Handler] Found text in ${part.type}:`,
                fallbackContent.substring(0, 100)
              );

              // 🔥 FILTER OUT TOOL CALL JSON: Don't add tool call JSON to content
              let shouldAddContent = true;
              try {
                const trimmed = fallbackContent.trim();

                // Check if this looks like tool call JSON
                if (
                  (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
                  (trimmed.startsWith("[") && trimmed.endsWith("]"))
                ) {
                  const parsed = JSON.parse(trimmed);

                  // Check for various tool call patterns
                  const looksLikeTool =
                    // Standard tool call format
                    (parsed &&
                      typeof parsed === "object" &&
                      "name" in parsed &&
                      "arguments" in parsed) ||
                    // Function call format
                    (parsed &&
                      typeof parsed === "object" &&
                      "function" in parsed) ||
                    // Tool invocation format
                    (parsed &&
                      typeof parsed === "object" &&
                      "tool" in parsed) ||
                    // Weather tool specific format
                    (parsed &&
                      typeof parsed === "object" &&
                      "date" in parsed &&
                      "forecast_type" in parsed &&
                      "location" in parsed) ||
                    // Search tool format
                    (parsed &&
                      typeof parsed === "object" &&
                      "query" in parsed &&
                      typeof parsed.query === "string") ||
                    // Calculator tool format
                    (parsed &&
                      typeof parsed === "object" &&
                      "expression" in parsed) ||
                    // Generic tool parameter format
                    (parsed &&
                      typeof parsed === "object" &&
                      ("input" in parsed ||
                        "prompt" in parsed ||
                        "text" in parsed ||
                        "url" in parsed ||
                        "code" in parsed)) ||
                    // Array of tool calls
                    (Array.isArray(parsed) &&
                      parsed.length > 0 &&
                      parsed.every(
                        (item) =>
                          item &&
                          typeof item === "object" &&
                          ("name" in item ||
                            "function" in item ||
                            "tool" in item)
                      ));

                  if (looksLikeTool) {
                    console.log(
                      `[Fallback Handler] Filtered out tool call JSON from ${part.type}:`,
                      trimmed.substring(0, 100)
                    );
                    shouldAddContent = false;
                  }
                }
              } catch {
                // If parsing fails, treat as regular content
              }

              if (shouldAddContent) {
                accumulatedContent += fallbackContent;

                // Update contentSequence
                if (
                  contentSequence.length > 0 &&
                  contentSequence[contentSequence.length - 1].type === "content"
                ) {
                  contentSequence[contentSequence.length - 1].text +=
                    fallbackContent;
                } else {
                  contentSequence.push({
                    type: "content",
                    text: fallbackContent,
                    timestamp: Date.now(),
                  });
                }

                // Force immediate update for fallback content
                if (hasCreatedMessage && messageId) {
                  const updateObj: any = {
                    messageId,
                    content: accumulatedContent,
                    toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
                    contentSequence: contentSequence,
                  };

                  // Only include thinking/reasoning if user has enabled it
                  if (accumulatedReasoning) {
                    updateObj.thinking = accumulatedReasoning;
                  }

                  await ctx.runMutation(api.messages.update, updateObj);
                  console.log(
                    `[Fallback Update] Content: ${accumulatedContent.length} chars`
                  );
                }
              }
            }
          }
        } // Close the for await loop
      } catch (streamError) {
        // Handle both synchronous errors (agent.stream() failure) and async errors (stream part errors)
        console.error(
          `[Streaming Error] Error during stream processing for ${actualProvider}/${actualModelId}:`,
          streamError
        );

        // Re-throw error to provider fallback logic for proper handling
        throw streamError;
      }

      // 🔥 FORCE FINAL STREAMING UPDATE: Catch any remaining content that didn't trigger throttle
      if (hasCreatedMessage && messageId) {
        const updateObj: any = {
          messageId,
          content: accumulatedContent,
          toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
          contentSequence: contentSequence,
        };

        // Only include thinking/reasoning if user has enabled it
        if (accumulatedReasoning) {
          updateObj.thinking = accumulatedReasoning;
        }

        await ctx.runMutation(api.messages.update, updateObj);
        console.log(
          `[Streaming Update] Final Force Update: ${accumulatedContent.length} chars, ${toolCalls.length} tools`
        );
      }

      // Get final usage and response info
      let usage;
      try {
        usage = result
          ? await result.usage
          : { inputTokens: 0, outputTokens: 0, totalTokens: 0 };
      } catch (err) {
        console.warn("Failed to get usage info:", err);
        usage = { inputTokens: 0, outputTokens: 0, totalTokens: 0 };
      }
      const finalContent = accumulatedContent.trim();

      // Save the completed message
      if (!hasCreatedMessage) {
        messageId = await ctx.runMutation(
          api.messages.prepareAssistantMessage,
          {
            conversationId: args.conversationId,
            branchId: branchId,
          }
        );
      }

      // 🔥 FINAL UPDATE: Ensure final content and metrics are saved (bypasses throttling)
      const finalUpdateObj: any = {
        messageId,
        content: finalContent,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
        contentSequence: contentSequence,
        generationMetrics: {
          provider: actualProvider,
          model: actualModelId,
          inputTokens: usage?.inputTokens || 0,
          outputTokens: usage?.outputTokens || 0,
          tokensUsed: usage?.totalTokens || 0,
          generationTimeMs: Date.now() - generationStartTime,
          temperature,
          finishReason: stepFinishReason,
        },
      };

      // Only include thinking/reasoning if user has enabled it
      if (accumulatedReasoning) {
        finalUpdateObj.thinking = accumulatedReasoning;
      }

      await ctx.runMutation(api.messages.update, finalUpdateObj);

      // Clear generation state
      await ctx.runMutation(api.conversations.setGenerationState, {
        conversationId: args.conversationId,
        isGenerating: false,
      });

      // Deduct credits for built-in provider keys if applicable
      if (!usingUserKey) {
        const actualInputTokens = usage?.inputTokens || 0;
        const actualOutputTokens = usage
          ? (usage.outputTokens ?? 0) + (usage.reasoningTokens ?? 0)
          : Math.ceil(finalContent.length / 4) +
            Math.ceil((accumulatedReasoning || "").length / 4);

        // Calculate per-call tool charges
        let extraToolDollars = 0;
        if (toolCalls.length > 0) {
          const modelLower = (actualModelId || "").toLowerCase();
          const webSearchCostPerCall = modelLower.startsWith("gpt-4")
            ? 25 / 1000 // $0.025
            : modelLower.includes("o3") || modelLower.includes("o4")
              ? 10 / 1000 // $0.01
              : 0;

          for (const tc of toolCalls) {
            if (tc.name === "code_interpreter") extraToolDollars += 0.03;
            if (tc.name === "web_search_preview")
              extraToolDollars += webSearchCostPerCall;
          }
        }

        if (
          actualInputTokens > 0 ||
          actualOutputTokens > 0 ||
          extraToolDollars > 0
        ) {
          try {
            await ctx.runMutation(api.usage.deductCredits, {
              model: actualModelId,
              inputTokens: actualInputTokens,
              outputTokens: actualOutputTokens,
              extraDollars: extraToolDollars,
              provider: actualProvider,
            });
          } catch (creditError) {
            console.warn("Failed to deduct credits:", creditError);
            // Continue without failing the entire request
          }
        }
      }

      return {
        messageId,
        content: finalContent,
        usingUserKey,
        generationMetrics: {
          provider: actualProvider,
          model: actualModelId,
          inputTokens: usage?.inputTokens || 0,
          outputTokens: usage?.outputTokens || 0,
          tokensUsed: usage?.totalTokens || 0,
          generationTimeMs: Date.now() - generationStartTime,
          temperature,
          finishReason: stepFinishReason,
        },
      };

      // The following code is now replaced by the v5 pattern above
      /*
      let accumulatedContent = "";
      const toolCalls: any[] = [];
      const contentSequence: any[] = [];
      let currentContentChunk = "";
      let totalTokens = 0;
      let promptTokens = 0;
      let completionTokens = 0;
      let accumulatedReasoning: string | undefined = undefined;
      let hasError = false;
      let errorMessage = "";
      let pendingCanvasData: any = undefined;
      let lastUpdateTime = Date.now();
      const UPDATE_INTERVAL = 400;
      let lastCheckTime = Date.now();
      const CHECK_INTERVAL = 500;

      let hasCreatedMessage = false; // New flag: Lazily create the message only when first content arrives

      // Process the full stream so we can detect tool calls in real time
      for await (const part of result.fullStream) {
        // Check for abort signal
        if (abortController?.signal.aborted) {
          const abortReason =
            abortController.signal.reason?.message || "Request was aborted";
          throw new Error(`Stream aborted: ${abortReason}`);
        }

        // Periodic check for cancellation
        const nowCheck = Date.now();
        if (nowCheck - lastCheckTime >= CHECK_INTERVAL) {
          const isCancelled = await ctx.runQuery(
            api.conversations.checkCancellation,
            {
              conversationId: args.conversationId,
            }
          );

          if (isCancelled) {
            abortController?.abort(new Error("User cancelled generation"));

            // Ensure message exists before trying to update it
            if (!hasCreatedMessage) {
              messageId = await ctx.runMutation(
                api.messages.prepareAssistantMessage,
                {
                  conversationId: args.conversationId,
                  branchId: branchId,
                }
              );
              hasCreatedMessage = true;
            }

            await ctx.runMutation(api.messages.update, {
              messageId,
              content: accumulatedContent || "Generation was stopped by user.",
              thinking: accumulatedReasoning,
              toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
              contentSequence: contentSequence,
            });

            // Clear the cancellation flag and generation state
            await ctx.runMutation(api.conversations.clearCancellation, {
              conversationId: args.conversationId,
            });
            await ctx.runMutation(api.conversations.setGenerationState, {
              conversationId: args.conversationId,
              isGenerating: false,
            });

            clearTimeout(timeoutId);
            return {
              messageId,
              content: accumulatedContent || "Generation was stopped by user.",
              usingUserKey,
              generationMetrics: {
                provider: actualProvider,
                model: actualModelId,
                generationTimeMs: Date.now() - generationStartTime,
                temperature,
                attemptedProviders: attemptedProviders.concat(actualProvider),
                fallbackUsed: attemptedProviders.length > 0,
              },
            };
          }
          lastCheckTime = nowCheck;
        }

        const partStartTime = Date.now();

        // Handle error parts in the stream
        if (part.type === "error") {
          hasError = true;
          errorMessage =
            part.error?.message || "Unknown streaming error occurred";
          console.error(
            `Stream error for ${actualProvider}/${actualModelId}:`,
            part.error
          );

          // Ensure message exists before trying to update it
          if (!hasCreatedMessage) {
            messageId = await ctx.runMutation(
              api.messages.prepareAssistantMessage,
              {
                conversationId: args.conversationId,
                branchId: branchId,
              }
            );
            hasCreatedMessage = true;
          }

          // Immediate update on error
          await ctx.runMutation(api.messages.update, {
            messageId,
            content: accumulatedContent || `Error: ${errorMessage}`,
            thinking: accumulatedReasoning,
            isError: true,
          });

          // Don't break here - let the stream complete and handle in outer catch
          continue;
        }

        // Text deltas
        if (part.type === 'text') {
          const newDelta = part.textDelta ?? "";
          if (newDelta) {
            // 🔥 FILTER OUT TOOL CALL JSON: Don't add tool call JSON to content
            let shouldAddDelta = true;
            try {
              const trimmed = newDelta.trim();

              // Check if this delta looks like tool call JSON
              if (
                (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
                (trimmed.startsWith("[") && trimmed.endsWith("]"))
              ) {
                const parsed = JSON.parse(trimmed);

                // Check for various tool call patterns
                const looksLikeTool =
                  // Standard tool call format
                  (parsed && typeof parsed === "object" && "name" in parsed && "arguments" in parsed) ||
                  // Function call format
                  (parsed && typeof parsed === "object" && "function" in parsed) ||
                  // Tool invocation format
                  (parsed && typeof parsed === "object" && "tool" in parsed) ||
                  // Weather tool specific format
                  (parsed && typeof parsed === "object" && "date" in parsed && "forecast_type" in parsed && "location" in parsed) ||
                  // Search tool format
                  (parsed && typeof parsed === "object" && "query" in parsed && typeof parsed.query === "string") ||
                  // Calculator tool format
                  (parsed && typeof parsed === "object" && "expression" in parsed) ||
                  // Generic tool parameter format
                  (parsed && typeof parsed === "object" &&
                    (("input" in parsed) || ("prompt" in parsed) || ("text" in parsed) || ("url" in parsed) || ("code" in parsed))
                  ) ||
                  // Array of tool calls
                  (Array.isArray(parsed) && parsed.length > 0 && parsed.every(item =>
                    item && typeof item === "object" && ("name" in item || "function" in item || "tool" in item)
                  ));

                if (looksLikeTool) {
                  console.log(
                    `[Text Delta Filter] Filtered out tool call JSON from text delta:`,
                    trimmed.substring(0, 100)
                  );
                  shouldAddDelta = false;
                }
              }
            } catch {
              // If parsing fails, treat as regular content
            }

            if (shouldAddDelta) {
              // NEW: Lazily create the message on first content
              if (!hasCreatedMessage) {
                messageId = await ctx.runMutation(
                  api.messages.prepareAssistantMessage,
                  {
                    conversationId: args.conversationId,
                    branchId: branchId,
                  }
                );
                hasCreatedMessage = true;
              }

              // Track time to first token if this is the first content
              if (!timeToFirstToken) {
                timeToFirstToken = Date.now() - generationStartTime;
              }
              if (!timeToFirstContent && accumulatedContent.length === 0) {
                timeToFirstContent = Date.now() - generationStartTime;
              }

              accumulatedContent += newDelta;
              currentContentChunk += newDelta;
            }
          }
        }

        // Reasoning deltas
        if (part.type === "reasoning-delta") {
          const reasoningDelta =
            part.textDelta ?? part.reasoningDelta ?? part.reasoningText ?? "";
          if (reasoningDelta) {
            // NEW: Lazily create the message on first reasoning (treat as content)
            if (!hasCreatedMessage) {
              messageId = await ctx.runMutation(
                api.messages.prepareAssistantMessage,
                {
                  conversationId: args.conversationId,
                  branchId: branchId,
                }
              );
              hasCreatedMessage = true;
            }

            // Track reasoning start time
            if (!reasoningStartTime) {
              reasoningStartTime = Date.now();
            }

            accumulatedReasoning =
              (accumulatedReasoning || "") + reasoningDelta;
          }
        }

        // Native thinking from providers (Google, Anthropic, OpenAI)
        if (part.type === "reasoning") {
          const reasoningText = part.textDelta ?? part.reasoningText ?? "";
          if (reasoningText) {
            // NEW: Lazily create the message on first reasoning
            if (!hasCreatedMessage) {
              messageId = await ctx.runMutation(
                api.messages.prepareAssistantMessage,
                {
                  conversationId: args.conversationId,
                  branchId: branchId,
                }
              );
              hasCreatedMessage = true;
            }

            // Track reasoning start time
            if (!reasoningStartTime) {
              reasoningStartTime = Date.now();
            }

            accumulatedReasoning = (accumulatedReasoning || "") + reasoningText;
          }
        }

        // Handle reasoning finish
        if (part.type === "reasoning-finish") {
          accumulatedReasoning =
            part.text ?? part.reasoningText ?? accumulatedReasoning;

          // Track reasoning end time
          if (reasoningStartTime && !reasoningEndTime) {
            reasoningEndTime = Date.now();
          }
        }

        // Track token usage if available
        if (part.type === "finish" && part.usage) {
          totalTokens = part.usage.totalTokens ?? totalTokens;
          promptTokens = part.usage.inputTokens ?? promptTokens;
          completionTokens = part.usage.outputTokens ?? completionTokens;
        }

        // Tool calls emitted by the model
        if (part.type === "tool-call") {
          // NEW: Lazily create the message on first tool call (treat as content equivalent)
          if (!hasCreatedMessage) {
            messageId = await ctx.runMutation(
              api.messages.prepareAssistantMessage,
              {
                conversationId: args.conversationId,
                branchId: branchId,
              }
            );
            hasCreatedMessage = true;
          }

          // Track time to first tool if not set
          if (!timeToFirstTool) {
            timeToFirstTool = Date.now() - generationStartTime;
          }
          // Track tool execution start time
          if (!toolExecutionStartTime) {
            toolExecutionStartTime = Date.now();
          }

          // Finalize current content chunk if there's content
          if (currentContentChunk.trim()) {
            // 🔥 FILTER OUT TOOL CALL JSON: Don't add tool call JSON to content sequence
            let shouldAddChunk = true;
            try {
              const trimmed = currentContentChunk.trim();

              // Check if this looks like tool call JSON
              if (
                (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
                (trimmed.startsWith("[") && trimmed.endsWith("]"))
              ) {
                const parsed = JSON.parse(trimmed);

                // Check for various tool call patterns
                const looksLikeTool =
                  // Standard tool call format
                  (parsed && typeof parsed === "object" && "name" in parsed && "arguments" in parsed) ||
                  // Function call format
                  (parsed && typeof parsed === "object" && "function" in parsed) ||
                  // Tool invocation format
                  (parsed && typeof parsed === "object" && "tool" in parsed) ||
                  // Weather tool specific format
                  (parsed && typeof parsed === "object" && "date" in parsed && "forecast_type" in parsed && "location" in parsed) ||
                  // Search tool format
                  (parsed && typeof parsed === "object" && "query" in parsed && typeof parsed.query === "string") ||
                  // Calculator tool format
                  (parsed && typeof parsed === "object" && "expression" in parsed) ||
                  // Generic tool parameter format
                  (parsed && typeof parsed === "object" &&
                    (("input" in parsed) || ("prompt" in parsed) || ("text" in parsed) || ("url" in parsed) || ("code" in parsed))
                  ) ||
                  // Array of tool calls
                  (Array.isArray(parsed) && parsed.length > 0 && parsed.every(item =>
                    item && typeof item === "object" && ("name" in item || "function" in item || "tool" in item)
                  ));

                if (looksLikeTool) {
                  console.log(
                    `[Content Chunk Filter] Filtered out tool call JSON from content chunk:`,
                    trimmed.substring(0, 100)
                  );
                  shouldAddChunk = false;
                }
              }
            } catch {
              // If parsing fails, treat as regular content
            }

            if (shouldAddChunk) {
              contentSequence.push({
                type: "content",
                text: currentContentChunk.trim(),
                timestamp: Date.now(),
              });
            }
            currentContentChunk = "";
          }

          const toolCall = {
            id: part.toolCallId || `tool_${Date.now()}`,
            name: part.toolName,
            arguments: JSON.stringify(part.args),
            result: undefined,
            startTime: Date.now(),
          };

          toolCalls.push(toolCall);

          // Add tool to sequence
          contentSequence.push({
            type: "tool",
            toolCallId: toolCall.id,
            timestamp: Date.now(),
          });
        }

        // Tool results
        if (part.type === "tool-result") {
          // NEW: Ensure message exists even for pure tool calls
          if (!hasCreatedMessage) {
            messageId = await ctx.runMutation(
              api.messages.prepareAssistantMessage,
              {
                conversationId: args.conversationId,
                branchId: branchId,
              }
            );
            hasCreatedMessage = true;
          }

          // Track tool execution end time
          if (toolExecutionStartTime && !toolExecutionEndTime) {
            toolExecutionEndTime = Date.now();
          }

          // Handle thinking tool specially
          if (part.toolName === "thinking") {
            const thinkingText =
              typeof part.result === "string"
                ? part.result
                : JSON.stringify(part.result, null, 2);

            accumulatedReasoning =
              (accumulatedReasoning || "") + `\n${thinkingText}`;
          }

          // Handle canvas tool specially
          if (part.toolName === "canvas" && part.result) {
            try {
              const canvasData =
                typeof part.result === "string"
                  ? JSON.parse(part.result)
                  : part.result;

              pendingCanvasData = canvasData;
            } catch (error) {
              console.error("Failed to parse canvas data:", error);
            }
          }

          const toolCallIndex = toolCalls.findIndex(
            (call) => call.id === part.toolCallId
          );
          if (toolCallIndex >= 0) {
            toolCalls[toolCallIndex].result = JSON.stringify(part.result);
            toolCalls[toolCallIndex].endTime = Date.now();
          }

          // Check user preferences for separate tool output cards
          const userPreferences = await ctx.runQuery(api.preferences.get);
          if (userPreferences?.showToolOutputs) {
            await ctx.runMutation(api.messages.add, {
              conversationId: args.conversationId,
              branchId: branchId,
              role: "tool",
              content:
                typeof part.result === "string"
                  ? part.result
                  : JSON.stringify(part.result, null, 2),
              toolCallId: part.toolCallId,
            });
          }
        }

        // Batched update at the end of each iteration if interval has passed
        const now = Date.now();
        if (now - lastUpdateTime >= UPDATE_INTERVAL && hasCreatedMessage) {
          // Double-check that messageId exists before updating
          if (!messageId) {
            console.error(
              "Message ID is null during batched update, skipping..."
            );
            continue;
          }

          const updateObj: any = {
            messageId,
            content: accumulatedContent.trim(),
            thinking: accumulatedReasoning,
            toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
              contentSequence: contentSequence,
            contentSequence:
              contentSequence.length > 0 ? contentSequence : undefined,
          };

          if (pendingCanvasData) {
            updateObj.canvasData = pendingCanvasData;
            pendingCanvasData = undefined;
          }

          try {
            await ctx.runMutation(api.messages.update, updateObj);
          } catch (updateError) {
            console.error(
              "Failed to update message during streaming:",
              updateError
            );
            // Continue streaming even if update fails
          }
          lastUpdateTime = now;
        }

        // Brief pause for smooth streaming
        await new Promise((r) => setTimeout(r, 10));
      }

      clearTimeout(timeoutId);

      // If there was an error in the stream, throw it now
      if (hasError) {
        throw new Error(errorMessage || "Stream encountered an error");
      }

      // If no message was created (pure tool calls without text), clean up
      if (!hasCreatedMessage) {
        // Clear generation state since no message was produced
        await ctx.runMutation(api.conversations.setGenerationState, {
          conversationId: args.conversationId,
          isGenerating: false,
        });
        return {
          messageId: undefined,
          content: "Tool execution complete without text response",
          usingUserKey,
          generationMetrics: {
            provider: actualProvider,
            model: actualModelId,
            generationTimeMs: Date.now() - generationStartTime,
            temperature,
            attemptedProviders: attemptedProviders.concat(actualProvider),
            fallbackUsed: attemptedProviders.length > 0,
          },
        };
      }

      // Ensure we have a valid messageId before final update
      if (!messageId) {
        console.error(
          "Message ID is null at final update, creating message now..."
        );
        messageId = await ctx.runMutation(
          api.messages.prepareAssistantMessage,
          {
            conversationId: args.conversationId,
            branchId: branchId,
          }
        );
        hasCreatedMessage = true;
      }

      // Calculate generation metrics with enhanced timing
      const generationEndTime = Date.now();
      const totalGenerationTimeMs = generationEndTime - generationStartTime;

      const tokensPerSecond =
        completionTokens > 0 && totalGenerationTimeMs > 0
          ? completionTokens / (totalGenerationTimeMs / 1000)
          : 0;

      const generationMetrics = {
        provider: actualProvider,
        model: actualModelId,
        tokensUsed: totalTokens || completionTokens + promptTokens, // Use actual or fallback
        inputTokens:
          promptTokens ||
          Math.ceil(JSON.stringify(messagesWithSystemPrompt).length / 4), // Fallback estimate
        outputTokens:
          completionTokens || Math.ceil(accumulatedContent.length / 4), // Fallback estimate
        generationTimeMs: totalGenerationTimeMs,
        tokensPerSecond: Math.round(tokensPerSecond * 100) / 100,
        temperature,
        timeToFirstTokenMs: timeToFirstToken,
        timeToFirstContentMs: timeToFirstContent,
        // timeToFirstToolMs is omitted to maintain schema compatibility until migration
        reasoningTimeMs:
          reasoningStartTime && reasoningEndTime
            ? reasoningEndTime - reasoningStartTime
            : undefined,
        toolExecutionTimeMs:
          toolExecutionStartTime && toolExecutionEndTime
            ? toolExecutionEndTime - toolExecutionStartTime
            : undefined,
        attemptedProviders: attemptedProviders.concat(actualProvider),
        fallbackUsed: attemptedProviders.length > 0,
      };

      // Use the final, resolved values from the SDK for the canonical state
      let finalContent: string;
      let finalThinking: string | undefined;

      try {
        finalContent = (await result.text.text).trim();
        const reasoningTextRaw = (await result.reasoningText)?.trim();
        finalThinking = reasoningTextRaw && reasoningTextRaw.length > 0
          ? reasoningTextRaw
          : accumulatedReasoning?.trim() || "";
      } catch (resultError) {
        console.error(
          `Error getting final result for ${actualProvider}/${actualModelId}:`,
          resultError
        );
        // Fallback to accumulated content if final result fails
        finalContent = accumulatedContent.trim();
        finalThinking = accumulatedReasoning?.trim();
      }

      // Finalize any remaining content chunk
      if (currentContentChunk.trim()) {
        // 🔥 FILTER OUT TOOL CALL JSON: Don't add tool call JSON to final content sequence
        let shouldAddFinalChunk = true;
        try {
          const trimmed = currentContentChunk.trim();

          // Check if this looks like tool call JSON
          if (
            (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
            (trimmed.startsWith("[") && trimmed.endsWith("]"))
          ) {
            const parsed = JSON.parse(trimmed);

            // Check for various tool call patterns
            const looksLikeTool =
              // Standard tool call format
              (parsed && typeof parsed === "object" && "name" in parsed && "arguments" in parsed) ||
              // Function call format
              (parsed && typeof parsed === "object" && "function" in parsed) ||
              // Tool invocation format
              (parsed && typeof parsed === "object" && "tool" in parsed) ||
              // Weather tool specific format
              (parsed && typeof parsed === "object" && "date" in parsed && "forecast_type" in parsed && "location" in parsed) ||
              // Search tool format
              (parsed && typeof parsed === "object" && "query" in parsed && typeof parsed.query === "string") ||
              // Calculator tool format
              (parsed && typeof parsed === "object" && "expression" in parsed) ||
              // Generic tool parameter format
              (parsed && typeof parsed === "object" &&
                (("input" in parsed) || ("prompt" in parsed) || ("text" in parsed) || ("url" in parsed) || ("code" in parsed))
              ) ||
              // Array of tool calls
              (Array.isArray(parsed) && parsed.length > 0 && parsed.every(item =>
                item && typeof item === "object" && ("name" in item || "function" in item || "tool" in item)
              ));

            if (looksLikeTool) {
              console.log(
                `[Final Content Chunk Filter] Filtered out tool call JSON from final content chunk:`,
                trimmed.substring(0, 100)
              );
              shouldAddFinalChunk = false;
            }
          }
        } catch {
          // If parsing fails, treat as regular content
        }

        if (shouldAddFinalChunk) {
          contentSequence.push({
            type: "content",
            text: currentContentChunk.trim(),
            timestamp: Date.now(),
          });
        }
      }

      // Final update to the message
      const finalUpdateObj: any = {
        messageId,
        content:
          finalContent ||
          "I apologize, but I couldn't generate a response. The model may have returned empty content or encountered an issue during generation.",
        thinking: finalThinking || undefined,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
              contentSequence: contentSequence,
        contentSequence:
          contentSequence.length > 0 ? contentSequence : undefined,
        generationMetrics,
      };

      if (pendingCanvasData) {
        finalUpdateObj.canvasData = pendingCanvasData;
      }

      try {
        await ctx.runMutation(api.messages.update, finalUpdateObj);
      } catch (finalUpdateError) {
        console.error(
          "Failed to perform final message update:",
          finalUpdateError
        );
        // Try to create a new message as fallback
        try {
          await ctx.runMutation(api.messages.add, {
            conversationId: args.conversationId,
            branchId: branchId,
            role: "assistant",
            content:
              finalContent ||
              "Response generated successfully but update failed",
            thinking: finalThinking,
            toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
              contentSequence: contentSequence,
            generationMetrics,
          });
        } catch (fallbackError) {
          console.error(
            "Fallback message creation also failed:",
            fallbackError
          );
        }
      }

      // Clear the cancellation flag and generation state
      await ctx.runMutation(api.conversations.clearCancellation, {
        conversationId: args.conversationId,
      });

      // Schedule Vectorize indexing with full assistant content (non-blocking)
      try {
        if (accumulatedContent.trim()) {
          await ctx.scheduler.runAfter(0, internal.vectorize.indexMessage, {
            messageId,
            conversationId: args.conversationId,
            userId: userId,
            branchId,
            text: accumulatedContent.trim(),
          });
        }
      } catch (err) {
        console.error(
          "Failed to schedule Vectorize indexing for assistant message:",
          err
        );
      }

      // Clear generation state
      await ctx.runMutation(api.conversations.setGenerationState, {
        conversationId: args.conversationId,
        isGenerating: false,
      });

      // Update usage if using built-in keys
      if (!usingUserKey) {
        // Deduct credits based on actual token usage
        const actualInputTokens = generationMetrics.inputTokens || 0;
        const actualOutputTokens = generationMetrics.outputTokens || 0;

        // Calculate per-call tool charges
        let extraToolDollars = 0;
        if (toolCalls.length > 0) {
          const modelLower = model.toLowerCase();
          const webSearchCostPerCall = modelLower.startsWith("gpt-4")
            ? 25 / 1000
            : modelLower.includes("o3") || modelLower.includes("o4")
              ? 10 / 1000
              : 0;

          toolCalls.forEach((tc) => {
            if (tc.name === "code_interpreter") extraToolDollars += 0.03;
            if (tc.name === "web_search_preview")
              extraToolDollars += webSearchCostPerCall;
          });
        }

        if (
          actualInputTokens > 0 ||
          actualOutputTokens > 0 ||
          extraToolDollars > 0
        ) {
          try {
            await ctx.runMutation(api.usage.deductCredits, {
              model: actualModelId,
              inputTokens: actualInputTokens,
              outputTokens: actualOutputTokens,
              extraDollars: extraToolDollars,
              provider: actualProvider,
            });
          } catch (creditError) {
            console.warn("Failed to deduct credits:", creditError);
            // Don't fail the whole request for credit deduction errors
          }
        }
      }
      */

      // This return is now replaced by the v5 toUIMessageStreamResponse above
      /*
      return {
        messageId,
        content: finalContent,
        usingUserKey,
        generationMetrics,
      };
      */
    } catch (error) {
      // Log the error for this provider attempt
      console.error(`[Provider Fallback] Error with ${actualProvider}:`, error);
      lastError = error instanceof Error ? error : new Error(String(error));
      attemptedProviders.push(actualProvider);

      // Helper function to extract error message from various nested locations
      interface ExtendedError extends Error {
        data?: {
          error?: {
            message?: string;
          };
        };
        responseBody?: string;
      }

      const getErrorMessage = (error: any): string => {
        if (typeof error === "string") {
          return error;
        }
        if (error instanceof Error) {
          // Check multiple possible locations for the actual error message
          const extendedError = error as ExtendedError;
          return (
            error.message ||
            extendedError.data?.error?.message ||
            extendedError.responseBody ||
            String(error)
          );
        }
        return String(error);
      };

      // Check if this is a retryable error with case-insensitive matching
      const errorMessage = getErrorMessage(error).toLowerCase();
      const _isRetryableError =
        errorMessage.includes("rate limit") ||
        errorMessage.includes("timeout") ||
        errorMessage.includes("503") ||
        errorMessage.includes("502") ||
        errorMessage.includes("500") ||
        errorMessage.includes("429") ||
        errorMessage.includes("authentication") ||
        errorMessage.includes("api key") ||
        errorMessage.includes("invalid") ||
        errorMessage.includes("unauthorized");

      // Only stop when we've attempted every provider. Always continue to the
      // next provider (when one exists) irrespective of the error type so that
      // provider priority and fallback work as expected.
      if (providerIndex === validProvidersToTry.length - 1) {
        console.log(
          `[Provider Fallback] No more providers to try — all providers exhausted`
        );
        break;
      }

      console.log(
        `[Provider Fallback] Retryable error with ${actualProvider}, trying next provider`
      );

      // Clear timeout for this attempt
      if (abortController) {
        abortController.abort();
        abortController = null;
      }

      // Continue to next provider
      continue;
    }
  }

  // If we get here, all providers failed
  if (lastError) {
    // Clear timeout if it exists
    if (abortController) {
      abortController.abort();
    }

    let errorMessage: string;

    // Handle timeout errors specifically
    if (lastError instanceof Error && lastError.message.includes("timeout")) {
      const timeoutMinutes = actualModelId.includes("deep-research")
        ? 60
        : actualProvider === "google"
          ? 10
          : 5;
      errorMessage = `All providers failed. The last attempt with ${actualProvider} model (${actualModelId}) timed out after ${timeoutMinutes} minutes. ${actualModelId.includes("deep-research") ? "Deep research models require more time for complex reasoning." : "This can happen with complex requests or when the model is under heavy load."} Attempted providers: ${attemptedProviders.join(", ")}. Please try again or consider using a different model.`;
    } else if (
      lastError instanceof Error &&
      lastError.message.includes("aborted")
    ) {
      errorMessage = `Request was cancelled: ${lastError.message}`;
    } else {
      errorMessage = `All providers failed. ${getUserFriendlyErrorMessage(lastError, actualProvider, usingUserKey)} Attempted providers: ${attemptedProviders.join(", ")}.`;
    }

    console.error(
      `Generation error after trying all providers. Last error from ${actualProvider}/${actualModelId}:`,
      lastError
    );

    // Clear generation state on error
    try {
      await ctx.runMutation(api.conversations.clearCancellation, {
        conversationId: args.conversationId,
      });
      await ctx.runMutation(api.conversations.setGenerationState, {
        conversationId: args.conversationId,
        isGenerating: false,
      });
    } catch {
      // Ignore clearance errors
    }

    // Update the existing message with error information
    if (messageId) {
      try {
        await ctx.runMutation(api.messages.update, {
          messageId,
          content: errorMessage,
          generationMetrics: {
            provider: actualProvider,
            model: actualModelId,
            generationTimeMs: 0,
            attemptedProviders,
            fallbackUsed: attemptedProviders.length > 1,
          },
          isError: true,
        });
      } catch {
        // If updating fails, create a new message as fallback
        await ctx.runMutation(api.messages.add, {
          conversationId: args.conversationId,
          branchId: branchId,
          role: "assistant",
          content: errorMessage,
          generationMetrics: {
            provider: actualProvider,
            model: actualModelId,
            generationTimeMs: 0,
            attemptedProviders,
            fallbackUsed: attemptedProviders.length > 1,
          },
          isError: true,
        });
      }
    } else {
      // If messageId is null, create a new message
      await ctx.runMutation(api.messages.add, {
        conversationId: args.conversationId,
        branchId: branchId,
        role: "assistant",
        content: errorMessage,
        generationMetrics: {
          provider: actualProvider,
          model: actualModelId,
          generationTimeMs: 0,
          attemptedProviders,
          fallbackUsed: attemptedProviders.length > 1,
        },
        isError: true,
      });
    }

    return {
      messageId: messageId ?? undefined,
      error: errorMessage,
      usingUserKey,
      generationMetrics: {
        provider: actualProvider,
        model: actualModelId,
        generationTimeMs: 0,
        attemptedProviders,
        fallbackUsed: attemptedProviders.length > 1,
      },
    };
  } else {
    // This shouldn't happen, but handle it gracefully
    const errorMessage = "Unknown error occurred during generation";
    console.error("No error captured but all providers failed");

    return {
      messageId: messageId ?? undefined,
      error: errorMessage,
      usingUserKey,
      generationMetrics: {
        provider: actualProvider,
        model: actualModelId,
        generationTimeMs: 0,
        attemptedProviders,
        fallbackUsed: attemptedProviders.length > 1,
      },
    };
  }
}
